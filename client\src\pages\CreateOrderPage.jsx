import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api";

export default function CreateOrderPage() {
  const [form, setForm] = useState({
    sellerType: "Vendor",
    sellerId: "",
    deliveryAddress: "",
    products: [{ product: "", quantity: 1 }]
  });
  const [sellers, setSellers] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    fetchSellers();
    fetchProducts();
  }, [form.sellerType]);

  const fetchSellers = async () => {
    try {
      const endpoint = form.sellerType === "Vendor" ? "/vendors" : "/suppliers";
      const response = await api.get(endpoint);
      setSellers(response.data);
    } catch (error) {
      console.error("Error fetching sellers:", error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await api.get("/products");
      setProducts(response.data);
    } catch (error) {
      console.error("Error fetching products:", error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // Calculate total amount
      const totalAmount = form.products.reduce((total, item) => {
        const product = products.find(p => p._id === item.product);
        return total + (product?.price || 0) * item.quantity;
      }, 0);

      const orderData = {
        ...form,
        totalAmount,
        products: form.products.filter(item => item.product && item.quantity > 0)
      };
      
      await api.post("/orders", orderData);
      alert("Order created successfully!");
      navigate("/orders");
    } catch (error) {
      setError(error.response?.data?.message || "Error creating order");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value
    });
  };

  const handleProductChange = (index, field, value) => {
    const newProducts = [...form.products];
    newProducts[index][field] = value;
    setForm({ ...form, products: newProducts });
  };

  const addProduct = () => {
    setForm({
      ...form,
      products: [...form.products, { product: "", quantity: 1 }]
    });
  };

  const removeProduct = (index) => {
    const newProducts = form.products.filter((_, i) => i !== index);
    setForm({ ...form, products: newProducts });
  };

  const calculateTotal = () => {
    return form.products.reduce((total, item) => {
      const product = products.find(p => p._id === item.product);
      return total + (product?.price || 0) * item.quantity;
    }, 0);
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '2rem' }}>
        <h1>Create New Order</h1>
        <p style={{ color: '#666' }}>Fill in the details to create a new order.</p>
      </div>

      {error && (
        <div style={{ 
          background: '#f8d7da', 
          color: '#721c24', 
          padding: '1rem', 
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{
        background: 'white',
        padding: '2rem',
        border: '1px solid #ddd',
        borderRadius: '8px'
      }}>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Seller Type *
            </label>
            <select
              name="sellerType"
              value={form.sellerType}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="Vendor">Vendor</option>
              <option value="Supplier">Supplier</option>
            </select>
          </div>

          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Select {form.sellerType} *
            </label>
            <select
              name="sellerId"
              value={form.sellerId}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="">Choose {form.sellerType}</option>
              {sellers.map((seller) => (
                <option key={seller._id} value={seller._id}>
                  {seller.businessName || seller.companyName || seller.user?.name || 'Unknown'}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '0.5rem', 
            fontWeight: 'bold' 
          }}>
            Delivery Address *
          </label>
          <textarea
            name="deliveryAddress"
            value={form.deliveryAddress}
            onChange={handleChange}
            required
            rows="3"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '1rem',
              resize: 'vertical'
            }}
            placeholder="Enter complete delivery address"
          />
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <label style={{ fontWeight: 'bold' }}>Products *</label>
            <button
              type="button"
              onClick={addProduct}
              style={{
                background: '#28a745',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Add Product
            </button>
          </div>

          {form.products.map((item, index) => (
            <div key={index} style={{
              display: 'grid',
              gridTemplateColumns: '2fr 1fr auto',
              gap: '1rem',
              alignItems: 'end',
              marginBottom: '1rem',
              padding: '1rem',
              border: '1px solid #eee',
              borderRadius: '4px'
            }}>
              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                  Product
                </label>
                <select
                  value={item.product}
                  onChange={(e) => handleProductChange(index, 'product', e.target.value)}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                >
                  <option value="">Select Product</option>
                  {products.map((product) => (
                    <option key={product._id} value={product._id}>
                      {product.name} - ${product.price?.toFixed(2)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                  Quantity
                </label>
                <input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => handleProductChange(index, 'quantity', parseInt(e.target.value))}
                  min="1"
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                />
              </div>

              <button
                type="button"
                onClick={() => removeProduct(index)}
                disabled={form.products.length === 1}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem',
                  borderRadius: '4px',
                  cursor: form.products.length === 1 ? 'not-allowed' : 'pointer'
                }}
              >
                Remove
              </button>
            </div>
          ))}
        </div>

        <div style={{ 
          background: '#f8f9fa', 
          padding: '1rem', 
          borderRadius: '4px', 
          marginBottom: '2rem',
          textAlign: 'right'
        }}>
          <strong>Total Amount: ${calculateTotal().toFixed(2)}</strong>
        </div>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 1,
              padding: '0.75rem',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '1rem',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Creating...' : 'Create Order'}
          </button>
          
          <button
            type="button"
            onClick={() => navigate("/orders")}
            style={{
              flex: 1,
              padding: '0.75rem',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '1rem',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
