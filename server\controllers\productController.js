const Product = require("../models/Product");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");

exports.createProduct = async (req, res) => {
  try {
    const { name, description, price, category, stock, supplierId } = req.body;

    let imagesArray = [];

    if (req.files && req.files.images && !Array.isArray(req.files.images)) {
      const uploadedImage = await uploadImagetoCloudinary(req.files.images, "products");
      imagesArray.push(uploadedImage.secure_url);
    }

    else if (req.files && Array.isArray(req.files.images)) {
      for (let file of req.files.images) {
        const uploadedImage = await uploadImagetoCloudinary(file, "products");
        imagesArray.push(uploadedImage.secure_url);
      }
    }

    const product = new Product({
      name,
      description,
      price,
      category,
      stock,
      images: imagesArray,
      supplier: supplierId,
    });

    await product.save();

    res.status(201).json({
      success: true,
      message: "Product created successfully",
      product,
    });
  } catch (error) {
    console.error("Product Creation Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create product",
      error: error.message,
    });
  }
};


exports.getAllProducts = async (req, res) => {
  try {
    const products = await Product.find();
    res.json(products);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getProductById = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) return res.status(404).json({ message: "Product not found" });
    res.json(product);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};