const Supplier = require("../models/Supplier");

exports.createSupplier = async (req, res) => {
  try {
    const newSupplier = await Supplier.create({
      user: req.user.id,
      location: req.body.location,
      supplierRating: req.body.supplierRating || 0,
      products: req.body.products ,
    });

    res.status(201).json(newSupplier);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getAllSuppliers = async (req, res) => {
  try {
    const suppliers = await Supplier.find().populate("user");
    res.json(suppliers);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getSupplierById = async (req, res) => {
  try {
    const supplier = await Supplier.findById(req.params.id).populate("user");
    if (!supplier) return res.status(404).json({ message: "Supplier not found" });
    res.json(supplier);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};