import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../api";

export default function ProductsPage() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedProduct, setSelectedProduct] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await api.get("/products");
      setProducts(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching products");
    } finally {
      setLoading(false);
    }
  };

  const viewProductDetails = async (productId) => {
    try {
      const response = await api.get(`/products/${productId}`);
      setSelectedProduct(response.data);
    } catch (error) {
      alert("Error fetching product details: " + (error.response?.data?.message || error.message));
    }
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading products...</div>;
  }

  if (error) {
    return (
      <div style={{ 
        background: '#f8d7da', 
        color: '#721c24', 
        padding: '1rem', 
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1>Products Management</h1>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Link 
            to="/products/create"
            style={{
              background: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Add Product
          </Link>
          <button 
            onClick={fetchProducts}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh
          </button>
        </div>
      </div>

      {products.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          No products found. <Link to="/products/create">Add the first product</Link>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {products.map((product) => (
            <div key={product._id} style={{
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '1.5rem',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ marginBottom: '1rem' }}>
                <h3 style={{ margin: '0 0 0.5rem 0', color: '#333' }}>{product.name}</h3>
                <p style={{ margin: '0', color: '#666', fontSize: '0.9rem' }}>
                  {product.description || 'No description available'}
                </p>
              </div>

              <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <span style={{ fontWeight: 'bold', color: '#28a745' }}>
                    ${product.price?.toFixed(2) || '0.00'}
                  </span>
                  <span style={{ 
                    background: product.stock > 10 ? '#28a745' : product.stock > 0 ? '#ffc107' : '#dc3545',
                    color: product.stock > 10 || product.stock === 0 ? 'white' : '#000',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.8rem'
                  }}>
                    Stock: {product.stock || 0}
                  </span>
                </div>
                
                <div style={{ fontSize: '0.9rem', color: '#666' }}>
                  <p style={{ margin: '0.25rem 0' }}>
                    <strong>Category:</strong> {product.category || 'N/A'}
                  </p>
                  <p style={{ margin: '0.25rem 0' }}>
                    <strong>SKU:</strong> {product.sku || 'N/A'}
                  </p>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <button
                  onClick={() => viewProductDetails(product._id)}
                  style={{
                    flex: 1,
                    background: '#17a2b8',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.875rem'
                  }}
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {selectedProduct && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <h2>Product Details</h2>
              <button
                onClick={() => setSelectedProduct(null)}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
            
            <div style={{ lineHeight: '1.6' }}>
              <p><strong>Name:</strong> {selectedProduct.name}</p>
              <p><strong>Description:</strong> {selectedProduct.description || 'N/A'}</p>
              <p><strong>Price:</strong> ${selectedProduct.price?.toFixed(2) || '0.00'}</p>
              <p><strong>Stock:</strong> {selectedProduct.stock || 0}</p>
              <p><strong>Category:</strong> {selectedProduct.category || 'N/A'}</p>
              <p><strong>SKU:</strong> {selectedProduct.sku || 'N/A'}</p>
              <p><strong>Created:</strong> {new Date(selectedProduct.createdAt).toLocaleDateString()}</p>
              <p><strong>Last Updated:</strong> {new Date(selectedProduct.updatedAt).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '2rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '4px',
        fontSize: '0.875rem',
        color: '#666'
      }}>
        Total Products: {products.length}
      </div>
    </div>
  );
}
