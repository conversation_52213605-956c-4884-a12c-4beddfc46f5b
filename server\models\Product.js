const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
  {
    name: { 
      type: String, 
      required: true 
    },
    price: { 
      type: Number, 
      required: true 
    },
    quantity: { 
      type: Number, 
      default: 0 
    },
    description: { 
      type: String
    },
    category: { 
      type: String
    },
    vendor: { 
      type: mongoose.Schema.Types.ObjectId, ref: 
      "Vendor" 
    },
    image: [
      {
        type: String,
        required: true,
      }
    ]
  },
  { timestamps: true }
);

module.exports = mongoose.model("Product", productSchema);
