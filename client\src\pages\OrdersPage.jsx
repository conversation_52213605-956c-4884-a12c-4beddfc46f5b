import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../api";

export default function OrdersPage() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedOrder, setSelectedOrder] = useState(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await api.get("/orders");
      setOrders(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching orders");
    } finally {
      setLoading(false);
    }
  };

  const viewOrderDetails = (order) => {
    setSelectedOrder(order);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return '#ffc107';
      case 'Processing': return '#17a2b8';
      case 'Shipped': return '#fd7e14';
      case 'Delivered': return '#28a745';
      case 'Cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading orders...</div>;
  }

  if (error) {
    return (
      <div style={{ 
        background: '#f8d7da', 
        color: '#721c24', 
        padding: '1rem', 
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1>Orders Management</h1>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Link 
            to="/orders/create"
            style={{
              background: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Create Order
          </Link>
          <button 
            onClick={fetchOrders}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh
          </button>
        </div>
      </div>

      {orders.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          No orders found. <Link to="/orders/create">Create the first order</Link>
        </div>
      ) : (
        <div style={{
          background: 'white',
          border: '1px solid #ddd',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Order ID</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Customer</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Seller</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Total</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Status</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Date</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order) => (
                <tr key={order._id} style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '1rem', fontFamily: 'monospace' }}>
                    {order._id.slice(-8)}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {order.user ? `${order.user.name} (${order.user.email})` : 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <span style={{
                      background: order.sellerType === 'Vendor' ? '#17a2b8' : '#ffc107',
                      color: order.sellerType === 'Supplier' ? '#000' : 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.8rem'
                    }}>
                      {order.sellerType}
                    </span>
                  </td>
                  <td style={{ padding: '1rem', fontWeight: 'bold', color: '#28a745' }}>
                    ${order.totalAmount?.toFixed(2) || '0.00'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <span style={{
                      background: getStatusColor(order.status),
                      color: order.status === 'Pending' ? '#000' : 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '12px',
                      fontSize: '0.875rem'
                    }}>
                      {order.status}
                    </span>
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {new Date(order.createdAt).toLocaleDateString()}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <button
                      onClick={() => viewOrderDetails(order)}
                      style={{
                        background: '#17a2b8',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '0.875rem'
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {selectedOrder && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '700px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <h2>Order Details</h2>
              <button
                onClick={() => setSelectedOrder(null)}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
            
            <div style={{ lineHeight: '1.6' }}>
              <p><strong>Order ID:</strong> {selectedOrder._id}</p>
              <p><strong>Customer:</strong> {selectedOrder.user?.name} ({selectedOrder.user?.email})</p>
              <p><strong>Seller Type:</strong> {selectedOrder.sellerType}</p>
              <p><strong>Total Amount:</strong> ${selectedOrder.totalAmount?.toFixed(2)}</p>
              <p><strong>Status:</strong> {selectedOrder.status}</p>
              <p><strong>Delivery Address:</strong> {selectedOrder.deliveryAddress}</p>
              <p><strong>Order Date:</strong> {new Date(selectedOrder.createdAt).toLocaleString()}</p>
              
              <div style={{ marginTop: '1.5rem' }}>
                <h4>Products:</h4>
                {selectedOrder.products && selectedOrder.products.length > 0 ? (
                  <ul style={{ paddingLeft: '1.5rem' }}>
                    {selectedOrder.products.map((item, index) => (
                      <li key={index} style={{ marginBottom: '0.5rem' }}>
                        {item.product?.name || 'Product'} - Quantity: {item.quantity}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p style={{ color: '#666' }}>No products listed</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '1rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '4px',
        fontSize: '0.875rem',
        color: '#666'
      }}>
        Total Orders: {orders.length}
      </div>
    </div>
  );
}
