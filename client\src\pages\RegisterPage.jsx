// src/pages/RegisterPage.jsx
import { useState } from "react";
import api from "../api";

export default function RegisterPage() {
  const [form, setForm] = useState({ name: "", email: "", password: "", phonenumber: "", role: "user" });

  const handleRegister = async () => {
    try {
      const res = await api.post("/register", form);
      alert("Registered! Now login.");
    } catch (err) {
      alert(err.response?.data?.error || "Error");
    }
  };

  return (
    <div>
      <h2>Register</h2>
      <input placeholder="Name" onChange={(e) => setForm({ ...form, name: e.target.value })} />
      <input placeholder="Email" onChange={(e) => setForm({ ...form, email: e.target.value })} />
      <input placeholder="Password" type="password" onChange={(e) => setForm({ ...form, password: e.target.value })} />
      <input placeholder="Phone" onChange={(e) => setForm({ ...form, phonenumber: e.target.value })} />
      <select onChange={(e) => setForm({ ...form, role: e.target.value })}>
        <option value="user">User</option>
        <option value="vendor">Vendor</option>
        <option value="supplier">Supplier</option>
      </select>
      <button onClick={handleRegister}>Register</button>
    </div>
  );
}
