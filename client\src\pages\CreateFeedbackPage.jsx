import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api";

export default function CreateFeedbackPage() {
  const [form, setForm] = useState({
    recipientType: "Vendor",
    recipientId: "",
    rating: 5,
    comment: ""
  });
  const [recipients, setRecipients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    fetchRecipients();
  }, [form.recipientType]);

  const fetchRecipients = async () => {
    try {
      const endpoint = form.recipientType === "Vendor" ? "/vendors" : "/suppliers";
      const response = await api.get(endpoint);
      setRecipients(response.data);
    } catch (error) {
      console.error("Error fetching recipients:", error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      await api.post("/feedbacks", form);
      alert("Feedback submitted successfully!");
      navigate("/feedback");
    } catch (error) {
      setError(error.response?.data?.message || "Error submitting feedback");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value
    });
  };

  const getRatingStars = (rating) => {
    return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto' }}>
      <div style={{ marginBottom: '2rem' }}>
        <h1>Give Feedback</h1>
        <p style={{ color: '#666' }}>Share your experience with vendors or suppliers.</p>
      </div>

      {error && (
        <div style={{ 
          background: '#f8d7da', 
          color: '#721c24', 
          padding: '1rem', 
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{
        background: 'white',
        padding: '2rem',
        border: '1px solid #ddd',
        borderRadius: '8px'
      }}>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Recipient Type *
            </label>
            <select
              name="recipientType"
              value={form.recipientType}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="Vendor">Vendor</option>
              <option value="Supplier">Supplier</option>
            </select>
          </div>

          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Select {form.recipientType} *
            </label>
            <select
              name="recipientId"
              value={form.recipientId}
              onChange={handleChange}
              required
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="">Choose {form.recipientType}</option>
              {recipients.map((recipient) => (
                <option key={recipient._id} value={recipient._id}>
                  {recipient.businessName || recipient.companyName || recipient.user?.name || 'Unknown'}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '0.5rem', 
            fontWeight: 'bold' 
          }}>
            Rating * ({form.rating}/5)
          </label>
          
          <div style={{ marginBottom: '1rem' }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
              {getRatingStars(parseInt(form.rating))}
            </div>
            
            <input
              type="range"
              name="rating"
              min="1"
              max="5"
              value={form.rating}
              onChange={handleChange}
              style={{
                width: '100%',
                height: '8px',
                borderRadius: '4px',
                background: '#ddd',
                outline: 'none'
              }}
            />
            
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              fontSize: '0.875rem', 
              color: '#666',
              marginTop: '0.5rem'
            }}>
              <span>1 - Poor</span>
              <span>2 - Fair</span>
              <span>3 - Good</span>
              <span>4 - Very Good</span>
              <span>5 - Excellent</span>
            </div>
          </div>
        </div>

        <div style={{ marginBottom: '2rem' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '0.5rem', 
            fontWeight: 'bold' 
          }}>
            Comment (Optional)
          </label>
          <textarea
            name="comment"
            value={form.comment}
            onChange={handleChange}
            rows="5"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '1rem',
              resize: 'vertical'
            }}
            placeholder="Share your detailed experience, what went well, what could be improved..."
          />
        </div>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 1,
              padding: '0.75rem',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '1rem',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Submitting...' : 'Submit Feedback'}
          </button>
          
          <button
            type="button"
            onClick={() => navigate("/feedback")}
            style={{
              flex: 1,
              padding: '0.75rem',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '1rem',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
