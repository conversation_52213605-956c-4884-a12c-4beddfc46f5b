import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../api";

export default function FeedbackPage() {
  const [feedbacks, setFeedbacks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchFeedbacks();
  }, []);

  const fetchFeedbacks = async () => {
    try {
      const response = await api.get("/feedbacks");
      setFeedbacks(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching feedback");
    } finally {
      setLoading(false);
    }
  };

  const getRatingStars = (rating) => {
    return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  const getRatingColor = (rating) => {
    if (rating >= 4) return '#28a745';
    if (rating >= 3) return '#ffc107';
    return '#dc3545';
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading feedback...</div>;
  }

  if (error) {
    return (
      <div style={{ 
        background: '#f8d7da', 
        color: '#721c24', 
        padding: '1rem', 
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1>Feedback Management</h1>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Link 
            to="/feedback/create"
            style={{
              background: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Give Feedback
          </Link>
          <button 
            onClick={fetchFeedbacks}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh
          </button>
        </div>
      </div>

      {feedbacks.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          No feedback found. <Link to="/feedback/create">Give the first feedback</Link>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gap: '1.5rem'
        }}>
          {feedbacks.map((feedback) => (
            <div key={feedback._id} style={{
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '1.5rem',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
                    <span style={{
                      background: feedback.recipientType === 'Vendor' ? '#17a2b8' : '#ffc107',
                      color: feedback.recipientType === 'Supplier' ? '#000' : 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '12px',
                      fontSize: '0.875rem',
                      fontWeight: 'bold'
                    }}>
                      {feedback.recipientType}
                    </span>
                    
                    <div style={{
                      color: getRatingColor(feedback.rating),
                      fontSize: '1.2rem'
                    }}>
                      {getRatingStars(feedback.rating)}
                    </div>
                    
                    <span style={{
                      background: getRatingColor(feedback.rating),
                      color: 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.875rem',
                      fontWeight: 'bold'
                    }}>
                      {feedback.rating}/5
                    </span>
                  </div>
                  
                  <p style={{ 
                    margin: '0', 
                    color: '#666', 
                    fontSize: '0.9rem' 
                  }}>
                    By: {feedback.user?.name || 'Anonymous'} ({feedback.user?.email || 'N/A'})
                  </p>
                </div>
                
                <div style={{ 
                  textAlign: 'right', 
                  color: '#666', 
                  fontSize: '0.875rem' 
                }}>
                  {new Date(feedback.createdAt).toLocaleDateString()}
                </div>
              </div>

              {feedback.comment && (
                <div style={{
                  background: '#f8f9fa',
                  padding: '1rem',
                  borderRadius: '4px',
                  borderLeft: '4px solid ' + getRatingColor(feedback.rating)
                }}>
                  <p style={{ 
                    margin: '0', 
                    lineHeight: '1.5',
                    fontStyle: 'italic'
                  }}>
                    "{feedback.comment}"
                  </p>
                </div>
              )}

              <div style={{ 
                marginTop: '1rem', 
                paddingTop: '1rem', 
                borderTop: '1px solid #eee',
                fontSize: '0.875rem',
                color: '#666'
              }}>
                <strong>Recipient ID:</strong> {feedback.recipientId}
              </div>
            </div>
          ))}
        </div>
      )}

      <div style={{ 
        marginTop: '2rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '4px',
        fontSize: '0.875rem',
        color: '#666',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <span>Total Feedback: {feedbacks.length}</span>
        <span>
          Average Rating: {feedbacks.length > 0 
            ? (feedbacks.reduce((sum, f) => sum + f.rating, 0) / feedbacks.length).toFixed(1)
            : 'N/A'
          }
        </span>
      </div>
    </div>
  );
}
