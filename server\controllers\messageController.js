const Message = require("../models/Message");
const Vendor = require("../models/Vendor");
const Supplier = require("../models/Supplier");

// Get messages between a vendor and supplier
const getMessages = async (req, res) => {
  const { vendorId, supplierId } = req.params;

  try {
    const messages = await Message.find({
      vendor: vendorId,
      supplier: supplierId,
    })
    .populate('sender', 'name email')
    .sort({ timestamp: 1 });

    res.status(200).json(messages);
  } catch (error) {
    console.error("Error fetching messages:", error);
    res.status(500).json({ error: "Failed to fetch messages" });
  }
};

// Send a new message
const sendMessage = async (req, res) => {
  try {
    const { vendorId, supplierId, content, messageType, subject } = req.body;
    const senderId = req.user.id;

    // Determine sender type based on user's role or association
    let senderType;
    const vendor = await Vendor.findOne({ user: senderId });
    const supplier = await Supplier.findOne({ user: senderId });

    if (vendor) {
      senderType = "vendor";
    } else if (supplier) {
      senderType = "supplier";
    } else {
      return res.status(400).json({ error: "User is not associated with any vendor or supplier" });
    }

    const newMessage = new Message({
      vendor: vendorId,
      supplier: supplierId,
      sender: senderId,
      senderType,
      content,
      messageType: messageType || "text",
      subject,
    });

    await newMessage.save();

    // Populate sender info before sending response
    await newMessage.populate('sender', 'name email');

    res.status(201).json(newMessage);
  } catch (error) {
    console.error("Error sending message:", error);
    res.status(500).json({ error: "Failed to send message" });
  }
};

// Get all conversations for a user
const getConversations = async (req, res) => {
  try {
    const userId = req.user.id;

    // Find if user is a vendor or supplier
    const vendor = await Vendor.findOne({ user: userId });
    const supplier = await Supplier.findOne({ user: userId });

    let conversations = [];

    if (vendor) {
      // Get conversations where this vendor is involved
      conversations = await Message.aggregate([
        { $match: { vendor: vendor._id } },
        {
          $group: {
            _id: { vendor: "$vendor", supplier: "$supplier" },
            lastMessage: { $last: "$content" },
            lastMessageTime: { $last: "$timestamp" },
            unreadCount: {
              $sum: {
                $cond: [
                  { $and: [{ $eq: ["$isRead", false] }, { $ne: ["$sender", userId] }] },
                  1,
                  0
                ]
              }
            }
          }
        },
        { $sort: { lastMessageTime: -1 } }
      ]);
    }

    if (supplier) {
      // Get conversations where this supplier is involved
      const supplierConversations = await Message.aggregate([
        { $match: { supplier: supplier._id } },
        {
          $group: {
            _id: { vendor: "$vendor", supplier: "$supplier" },
            lastMessage: { $last: "$content" },
            lastMessageTime: { $last: "$timestamp" },
            unreadCount: {
              $sum: {
                $cond: [
                  { $and: [{ $eq: ["$isRead", false] }, { $ne: ["$sender", userId] }] },
                  1,
                  0
                ]
              }
            }
          }
        },
        { $sort: { lastMessageTime: -1 } }
      ]);
      conversations = conversations.concat(supplierConversations);
    }

    // Populate vendor and supplier details
    await Message.populate(conversations, [
      { path: '_id.vendor', select: 'businessName location.address', populate: { path: 'user', select: 'name email' } },
      { path: '_id.supplier', select: 'location.address', populate: { path: 'user', select: 'name email' } }
    ]);

    res.status(200).json(conversations);
  } catch (error) {
    console.error("Error fetching conversations:", error);
    res.status(500).json({ error: "Failed to fetch conversations" });
  }
};

// Mark messages as read
const markAsRead = async (req, res) => {
  try {
    const { vendorId, supplierId } = req.params;
    const userId = req.user.id;

    await Message.updateMany(
      {
        vendor: vendorId,
        supplier: supplierId,
        sender: { $ne: userId }, // Don't mark own messages as read
        isRead: false
      },
      {
        isRead: true,
        readAt: new Date()
      }
    );

    res.status(200).json({ message: "Messages marked as read" });
  } catch (error) {
    console.error("Error marking messages as read:", error);
    res.status(500).json({ error: "Failed to mark messages as read" });
  }
};

module.exports = {
  getMessages,
  sendMessage,
  getConversations,
  markAsRead,
};
