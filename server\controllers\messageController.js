const Message = require("../models/Message");

const getMessages = async (req, res) => {
  const { vendorId, supplierId } = req.params;

  try {
    const messages = await Message.find({
      vendor: vendorId,
      supplier: supplierId,
    }).sort({ timestamp: 1 });

    res.status(200).json(messages);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch messages" });
  }
};

module.exports = {
  getMessages,
};
