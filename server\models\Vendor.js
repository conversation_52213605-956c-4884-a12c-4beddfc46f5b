const mongoose = require("mongoose");

const vendorSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },

    // Location details using GeoJSON
    location: {
      type: {
        type: String,
        enum: ["Point"],
        default: "Point",
      },
      coordinates: {
        type: [Number], // Format: [longitude, latitude]
        required: true,
      },
      address: {
        type: String,
        required: true,
      },
    },

    businessName: {
      type: String,
      required: true,
    },
   
    vendorRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },

    products: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
      },
    ],

    // Operating hours (optional)
    openTime: {
      type: String, // e.g. "09:00 AM"
    },
    closeTime: {
      type: String, // e.g. "08:00 PM"
    },
  },
  { timestamps: true }
);

vendorSchema.index({ location: "2dsphere" });

module.exports = mongoose.model("Vendor", vendorSchema);
