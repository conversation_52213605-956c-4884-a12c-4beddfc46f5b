const mongoose = require("mongoose");

const MessageSchema = new mongoose.Schema({
  // Participants in the conversation
  supplier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Supplier",
    required: true,
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Vendor",
    required: true,
  },

  // Message details
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User", // The actual user who sent the message
    required: true,
  },
  senderType: {
    type: String,
    enum: ["vendor", "supplier"],
    required: true,
  },
  content: {
    type: String,
    required: true,
    trim: true,
  },

  // Message metadata
  messageType: {
    type: String,
    enum: ["text", "request", "offer", "notification"],
    default: "text",
  },
  subject: {
    type: String,
    trim: true,
  },

  // Status tracking
  isRead: {
    type: Boolean,
    default: false,
  },
  readAt: {
    type: Date,
  },

  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true
});

// Index for efficient querying
MessageSchema.index({ supplier: 1, vendor: 1, timestamp: -1 });
MessageSchema.index({ sender: 1, timestamp: -1 });

module.exports = mongoose.model("Message", MessageSchema);