const Feedback = require("../models/Feedback");


exports.createFeedback = async (req, res) => {
  try {
    const { recipientType, recipientId, rating, comment } = req.body;

    if (!recipientType || !recipientId || !rating) {
      return res.status(400).json({
        success: false,
        message: "Recipient type, ID, and rating are required.",
      });
    }
    const validTypes = ["Vendor", "Supplier"];
    if (!validTypes.includes(recipientType)) {
      return res.status(400).json({
        success: false,
        message: "Invalid recipient type. Must be Vendor or Supplier.",
      });
    }
    const feedback = await Feedback.create({
      user: req.user.id, 
      recipientType,
      recipientId,
      rating,
      comment,
    });

    res.status(201).json({
      success: true,
      message: "Feedback submitted successfully.",
      feedback,
    });
  } catch (err) {
    console.error("Create Feedback Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while creating feedback.",
    });
  }
};


exports.getAllFeedbacks = async (req, res) => {
  try {
    const feedbacks = await Feedback.find({
      recipientId: req.params.id,
      recipientType: req.params.type,
    }).populate("user");
    res.json(feedbacks);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};