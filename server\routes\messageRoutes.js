const express = require("express");
const router = express.Router();
const {
  getMessages,
  sendMessage,
  getConversations,
  markAsRead
} = require("../controllers/messageController");
const { isAuth } = require("../middlewares/auth");

// Get all conversations for the authenticated user
router.get("/conversations", isAuth, getConversations);

// Get messages between specific vendor and supplier
router.get("/:vendorId/:supplierId", isAuth, getMessages);

// Send a new message
router.post("/send", isAuth, sendMessage);

// Mark messages as read
router.put("/:vendorId/:supplierId/read", isAuth, markAsRead);

module.exports = router;