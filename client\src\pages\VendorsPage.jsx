import { useState, useEffect } from "react";
import api from "../api";

export default function VendorsPage() {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedVendor, setSelectedVendor] = useState(null);

  useEffect(() => {
    fetchVendors();
  }, []);

  const fetchVendors = async () => {
    try {
      const response = await api.get("/vendors");
      setVendors(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching vendors");
    } finally {
      setLoading(false);
    }
  };

  const viewVendorDetails = async (vendorId) => {
    try {
      const response = await api.get(`/vendors/${vendorId}`);
      setSelectedVendor(response.data);
    } catch (error) {
      alert("Error fetching vendor details: " + (error.response?.data?.message || error.message));
    }
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading vendors...</div>;
  }

  if (error) {
    return (
      <div style={{ 
        background: '#f8d7da', 
        color: '#721c24', 
        padding: '1rem', 
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1>Vendors Management</h1>
        <button 
          onClick={fetchVendors}
          style={{
            background: '#007bff',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh
        </button>
      </div>

      {vendors.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          No vendors found.
        </div>
      ) : (
        <div style={{
          background: 'white',
          border: '1px solid #ddd',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Business Name</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Owner</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Address</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Rating</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Hours</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {vendors.map((vendor) => (
                <tr key={vendor._id} style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '1rem', fontWeight: 'bold' }}>{vendor.businessName}</td>
                  <td style={{ padding: '1rem' }}>
                    {vendor.user ? `${vendor.user.name} (${vendor.user.email})` : 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>{vendor.location?.address || 'N/A'}</td>
                  <td style={{ padding: '1rem' }}>
                    <span style={{
                      background: vendor.vendorRating >= 4 ? '#28a745' : 
                                 vendor.vendorRating >= 3 ? '#ffc107' : '#dc3545',
                      color: vendor.vendorRating >= 3 && vendor.vendorRating < 4 ? '#000' : 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.875rem'
                    }}>
                      {vendor.vendorRating.toFixed(1)} ⭐
                    </span>
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {vendor.openTime && vendor.closeTime 
                      ? `${vendor.openTime} - ${vendor.closeTime}` 
                      : 'Not specified'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <button
                      onClick={() => viewVendorDetails(vendor._id)}
                      style={{
                        background: '#17a2b8',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '0.875rem'
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {selectedVendor && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <h2>Vendor Details</h2>
              <button
                onClick={() => setSelectedVendor(null)}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
            
            <div style={{ lineHeight: '1.6' }}>
              <p><strong>Business Name:</strong> {selectedVendor.businessName}</p>
              <p><strong>Owner:</strong> {selectedVendor.user?.name} ({selectedVendor.user?.email})</p>
              <p><strong>Phone:</strong> {selectedVendor.user?.phonenumber}</p>
              <p><strong>Address:</strong> {selectedVendor.location?.address}</p>
              <p><strong>Coordinates:</strong> {selectedVendor.location?.coordinates?.join(', ')}</p>
              <p><strong>Rating:</strong> {selectedVendor.vendorRating.toFixed(1)} ⭐</p>
              <p><strong>Operating Hours:</strong> {selectedVendor.openTime && selectedVendor.closeTime 
                ? `${selectedVendor.openTime} - ${selectedVendor.closeTime}` 
                : 'Not specified'}</p>
              <p><strong>Products:</strong> {selectedVendor.products?.length || 0} products</p>
              <p><strong>Created:</strong> {new Date(selectedVendor.createdAt).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '1rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '4px',
        fontSize: '0.875rem',
        color: '#666'
      }}>
        Total Vendors: {vendors.length}
      </div>
    </div>
  );
}
