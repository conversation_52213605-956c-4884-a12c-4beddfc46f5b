import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../api";

export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  useEffect(() => {
    fetchSuppliers();
  }, []);

  const fetchSuppliers = async () => {
    try {
      const response = await api.get("/suppliers");
      setSuppliers(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching suppliers");
    } finally {
      setLoading(false);
    }
  };

  const viewSupplierDetails = async (supplierId) => {
    try {
      const response = await api.get(`/suppliers/${supplierId}`);
      setSelectedSupplier(response.data);
    } catch (error) {
      alert("Error fetching supplier details: " + (error.response?.data?.message || error.message));
    }
  };

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading suppliers...</div>;
  }

  if (error) {
    return (
      <div style={{ 
        background: '#f8d7da', 
        color: '#721c24', 
        padding: '1rem', 
        borderRadius: '4px',
        margin: '1rem 0'
      }}>
        {error}
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1>Suppliers Management</h1>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Link 
            to="/suppliers/create"
            style={{
              background: '#28a745',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Create Supplier
          </Link>
          <button 
            onClick={fetchSuppliers}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh
          </button>
        </div>
      </div>

      {suppliers.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          No suppliers found. <Link to="/suppliers/create">Create the first supplier</Link>
        </div>
      ) : (
        <div style={{
          background: 'white',
          border: '1px solid #ddd',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Company Name</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Contact Person</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Email</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Phone</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Rating</th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {suppliers.map((supplier) => (
                <tr key={supplier._id} style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '1rem', fontWeight: 'bold' }}>
                    {supplier.companyName || 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {supplier.user ? supplier.user.name : 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {supplier.user ? supplier.user.email : 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    {supplier.user ? supplier.user.phonenumber : 'N/A'}
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <span style={{
                      background: (supplier.supplierRating || 0) >= 4 ? '#28a745' : 
                                 (supplier.supplierRating || 0) >= 3 ? '#ffc107' : '#dc3545',
                      color: (supplier.supplierRating || 0) >= 3 && (supplier.supplierRating || 0) < 4 ? '#000' : 'white',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      fontSize: '0.875rem'
                    }}>
                      {(supplier.supplierRating || 0).toFixed(1)} ⭐
                    </span>
                  </td>
                  <td style={{ padding: '1rem' }}>
                    <button
                      onClick={() => viewSupplierDetails(supplier._id)}
                      style={{
                        background: '#17a2b8',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '0.875rem'
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {selectedSupplier && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <h2>Supplier Details</h2>
              <button
                onClick={() => setSelectedSupplier(null)}
                style={{
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
            
            <div style={{ lineHeight: '1.6' }}>
              <p><strong>Company Name:</strong> {selectedSupplier.companyName || 'N/A'}</p>
              <p><strong>Contact Person:</strong> {selectedSupplier.user?.name || 'N/A'}</p>
              <p><strong>Email:</strong> {selectedSupplier.user?.email || 'N/A'}</p>
              <p><strong>Phone:</strong> {selectedSupplier.user?.phonenumber || 'N/A'}</p>
              <p><strong>Rating:</strong> {(selectedSupplier.supplierRating || 0).toFixed(1)} ⭐</p>
              <p><strong>Products:</strong> {selectedSupplier.products?.length || 0} products</p>
              <p><strong>Created:</strong> {new Date(selectedSupplier.createdAt).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '1rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '4px',
        fontSize: '0.875rem',
        color: '#666'
      }}>
        Total Suppliers: {suppliers.length}
      </div>
    </div>
  );
}
