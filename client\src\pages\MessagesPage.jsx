import { useState, useEffect } from "react";
import api from "../api";

export default function MessagesPage() {
  const [vendors, setVendors] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [selectedVendor, setSelectedVendor] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState("");
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchVendorsAndSuppliers();
  }, []);

  const fetchVendorsAndSuppliers = async () => {
    try {
      const [vendorsRes, suppliersRes] = await Promise.all([
        api.get("/vendors"),
        api.get("/suppliers")
      ]);
      setVendors(vendorsRes.data);
      setSuppliers(suppliersRes.data);
    } catch (error) {
      setError("Error fetching vendors and suppliers");
    }
  };

  const fetchMessages = async () => {
    if (!selectedVendor || !selectedSupplier) {
      setError("Please select both a vendor and a supplier");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await api.get(`/messages/${selectedVendor}/${selectedSupplier}`);
      setMessages(response.data);
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching messages");
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ marginBottom: '2rem' }}>
        <h1>Messages</h1>
        <p style={{ color: '#666' }}>View messages between vendors and suppliers.</p>
      </div>

      {error && (
        <div style={{ 
          background: '#f8d7da', 
          color: '#721c24', 
          padding: '1rem', 
          borderRadius: '4px',
          marginBottom: '1rem'
        }}>
          {error}
        </div>
      )}

      <div style={{
        background: 'white',
        padding: '2rem',
        border: '1px solid #ddd',
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h3 style={{ marginTop: 0 }}>Select Conversation</h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr auto', gap: '1rem', alignItems: 'end' }}>
          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Select Vendor
            </label>
            <select
              value={selectedVendor}
              onChange={(e) => setSelectedVendor(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="">Choose Vendor</option>
              {vendors.map((vendor) => (
                <option key={vendor._id} value={vendor._id}>
                  {vendor.businessName || vendor.user?.name || 'Unknown Vendor'}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '0.5rem', 
              fontWeight: 'bold' 
            }}>
              Select Supplier
            </label>
            <select
              value={selectedSupplier}
              onChange={(e) => setSelectedSupplier(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '1rem'
              }}
            >
              <option value="">Choose Supplier</option>
              {suppliers.map((supplier) => (
                <option key={supplier._id} value={supplier._id}>
                  {supplier.companyName || supplier.user?.name || 'Unknown Supplier'}
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={fetchMessages}
            disabled={loading || !selectedVendor || !selectedSupplier}
            style={{
              padding: '0.75rem 1.5rem',
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: loading || !selectedVendor || !selectedSupplier ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Loading...' : 'Load Messages'}
          </button>
        </div>
      </div>

      {selectedVendor && selectedSupplier && (
        <div style={{
          background: 'white',
          border: '1px solid #ddd',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <div style={{
            background: '#f8f9fa',
            padding: '1rem',
            borderBottom: '1px solid #ddd'
          }}>
            <h3 style={{ margin: 0 }}>
              Conversation between{' '}
              <span style={{ color: '#17a2b8' }}>
                {vendors.find(v => v._id === selectedVendor)?.businessName || 'Vendor'}
              </span>
              {' '}and{' '}
              <span style={{ color: '#ffc107' }}>
                {suppliers.find(s => s._id === selectedSupplier)?.companyName || 'Supplier'}
              </span>
            </h3>
          </div>

          <div style={{ 
            minHeight: '400px',
            maxHeight: '600px',
            overflow: 'auto',
            padding: '1rem'
          }}>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
                Loading messages...
              </div>
            ) : messages.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
                No messages found for this conversation.
              </div>
            ) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {messages.map((message, index) => (
                  <div key={index} style={{
                    background: '#f8f9fa',
                    padding: '1rem',
                    borderRadius: '8px',
                    borderLeft: '4px solid #007bff'
                  }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      <strong style={{ color: '#333' }}>
                        {message.sender || 'Unknown Sender'}
                      </strong>
                      <span style={{ color: '#666', fontSize: '0.875rem' }}>
                        {message.timestamp ? new Date(message.timestamp).toLocaleString() : 'Unknown time'}
                      </span>
                    </div>
                    <p style={{ margin: 0, lineHeight: '1.5' }}>
                      {message.content || message.message || 'No message content'}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {!selectedVendor || !selectedSupplier ? (
        <div style={{
          background: '#e9ecef',
          padding: '2rem',
          borderRadius: '8px',
          textAlign: 'center',
          color: '#666'
        }}>
          <h3>Select a vendor and supplier to view their conversation</h3>
          <p>Choose both a vendor and a supplier from the dropdowns above to load their messages.</p>
        </div>
      ) : null}
    </div>
  );
}
