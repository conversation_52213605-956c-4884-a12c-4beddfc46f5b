import { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import api from "../api";

export default function MessagesPage() {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [messageType, setMessageType] = useState("text");
  const [subject, setSubject] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showNewMessageForm, setShowNewMessageForm] = useState(false);

  // For creating new conversations
  const [vendors, setVendors] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [selectedVendor, setSelectedVendor] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState("");

  useEffect(() => {
    fetchConversations();
    fetchVendorsAndSuppliers();
  }, []);

  // Fetch all conversations for the current user
  const fetchConversations = async () => {
    try {
      const response = await api.get("/messages/conversations");
      setConversations(response.data);
    } catch (error) {
      setError("Error fetching conversations");
    }
  };

  // Fetch vendors and suppliers for creating new conversations
  const fetchVendorsAndSuppliers = async () => {
    try {
      const [vendorsRes, suppliersRes] = await Promise.all([
        api.get("/vendors"),
        api.get("/suppliers")
      ]);
      setVendors(vendorsRes.data);
      setSuppliers(suppliersRes.data);
    } catch (error) {
      setError("Error fetching vendors and suppliers");
    }
  };

  // Fetch messages for a specific conversation
  const fetchMessages = async (vendorId, supplierId) => {
    setLoading(true);
    setError("");

    try {
      const response = await api.get(`/messages/${vendorId}/${supplierId}`);
      setMessages(response.data);

      // Mark messages as read
      await api.put(`/messages/${vendorId}/${supplierId}/read`);

      // Update conversation list to reflect read status
      fetchConversations();
    } catch (error) {
      setError(error.response?.data?.message || "Error fetching messages");
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  // Send a new message
  const sendMessage = async (e) => {
    e.preventDefault();

    if (!newMessage.trim()) {
      setError("Message content is required");
      return;
    }

    if (!selectedConversation && (!selectedVendor || !selectedSupplier)) {
      setError("Please select a conversation or create a new one");
      return;
    }

    try {
      const messageData = {
        vendorId: selectedConversation ? selectedConversation._id.vendor : selectedVendor,
        supplierId: selectedConversation ? selectedConversation._id.supplier : selectedSupplier,
        content: newMessage,
        messageType,
        subject: subject || undefined
      };

      await api.post("/messages/send", messageData);

      // Clear form
      setNewMessage("");
      setSubject("");
      setMessageType("text");

      // Refresh messages and conversations
      if (selectedConversation) {
        fetchMessages(selectedConversation._id.vendor, selectedConversation._id.supplier);
      } else {
        fetchMessages(selectedVendor, selectedSupplier);
        setShowNewMessageForm(false);
      }
      fetchConversations();

    } catch (error) {
      setError(error.response?.data?.error || "Error sending message");
    }
  };

  // Select a conversation
  const selectConversation = (conversation) => {
    setSelectedConversation(conversation);
    setShowNewMessageForm(false);
    fetchMessages(conversation._id.vendor, conversation._id.supplier);
  };

  // Start a new conversation
  const startNewConversation = () => {
    setSelectedConversation(null);
    setMessages([]);
    setShowNewMessageForm(true);
    setSelectedVendor("");
    setSelectedSupplier("");
  };

  return (
    <div style={{ display: 'flex', height: '80vh', gap: '1rem' }}>
      {/* Conversations Sidebar */}
      <div style={{
        width: '300px',
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '1rem'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <h3>Conversations</h3>
          <button
            onClick={startNewConversation}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '0.5rem',
              cursor: 'pointer',
              fontSize: '0.8rem'
            }}
          >
            New Message
          </button>
        </div>

        {error && (
          <div style={{
            background: '#f8d7da',
            color: '#721c24',
            padding: '0.5rem',
            borderRadius: '4px',
            marginBottom: '1rem',
            fontSize: '0.8rem'
          }}>
            {error}
          </div>
        )}

        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {conversations.length === 0 ? (
            <p style={{ color: '#666', textAlign: 'center' }}>No conversations yet</p>
          ) : (
            conversations.map((conversation, index) => (
              <div
                key={index}
                onClick={() => selectConversation(conversation)}
                style={{
                  padding: '0.75rem',
                  border: '1px solid #eee',
                  borderRadius: '4px',
                  marginBottom: '0.5rem',
                  cursor: 'pointer',
                  background: selectedConversation === conversation ? '#e3f2fd' : 'white'
                }}
              >
                <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                  {conversation._id.vendor?.businessName || 'Vendor'} ↔
                  {conversation._id.supplier?.user?.name || 'Supplier'}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.25rem' }}>
                  {conversation.lastMessage?.substring(0, 50)}...
                </div>
                <div style={{ fontSize: '0.7rem', color: '#999', marginTop: '0.25rem' }}>
                  {new Date(conversation.lastMessageTime).toLocaleDateString()}
                  {conversation.unreadCount > 0 && (
                    <span style={{
                      background: '#dc3545',
                      color: 'white',
                      borderRadius: '50%',
                      padding: '0.2rem 0.4rem',
                      marginLeft: '0.5rem',
                      fontSize: '0.6rem'
                    }}>
                      {conversation.unreadCount}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div style={{
        flex: 1,
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          padding: '1rem',
          borderBottom: '1px solid #eee',
          background: '#f8f9fa'
        }}>
          {selectedConversation ? (
            <h3>
              {selectedConversation._id.vendor?.businessName || 'Vendor'} ↔
              {selectedConversation._id.supplier?.user?.name || 'Supplier'}
            </h3>
          ) : showNewMessageForm ? (
            <h3>New Message</h3>
          ) : (
            <h3>Select a conversation</h3>
          )}
        </div>

        {/* Messages Display */}
        <div style={{
          flex: 1,
          padding: '1rem',
          overflowY: 'auto',
          maxHeight: '50vh'
        }}>
          {loading ? (
            <div style={{ textAlign: 'center', color: '#666' }}>Loading messages...</div>
          ) : messages.length === 0 ? (
            <div style={{ textAlign: 'center', color: '#666' }}>
              {selectedConversation || showNewMessageForm ? 'No messages yet. Start the conversation!' : 'Select a conversation to view messages'}
            </div>
          ) : (
            messages.map((message, index) => (
              <div
                key={index}
                style={{
                  marginBottom: '1rem',
                  padding: '0.75rem',
                  border: '1px solid #eee',
                  borderRadius: '8px',
                  background: message.sender._id === user?.id ? '#e3f2fd' : '#f8f9fa'
                }}
              >
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '0.5rem'
                }}>
                  <strong>{message.sender.name}</strong>
                  <span style={{ fontSize: '0.8rem', color: '#666' }}>
                    {new Date(message.timestamp).toLocaleString()}
                  </span>
                </div>
                {message.subject && (
                  <div style={{
                    fontWeight: 'bold',
                    marginBottom: '0.5rem',
                    color: '#495057'
                  }}>
                    Subject: {message.subject}
                  </div>
                )}
                <div>{message.content}</div>
                <div style={{
                  fontSize: '0.7rem',
                  color: '#6c757d',
                  marginTop: '0.5rem'
                }}>
                  Type: {message.messageType} | Sender: {message.senderType}
                  {message.isRead && <span> | Read</span>}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Message Form */}
        {(selectedConversation || showNewMessageForm) && (
          <div style={{
            padding: '1rem',
            borderTop: '1px solid #eee',
            background: '#f8f9fa'
          }}>
            {showNewMessageForm && (
              <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                      Select Vendor
                    </label>
                    <select
                      value={selectedVendor}
                      onChange={(e) => setSelectedVendor(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '0.5rem',
                        border: '1px solid #ddd',
                        borderRadius: '4px'
                      }}
                      required
                    >
                      <option value="">Choose a vendor...</option>
                      {vendors.map(vendor => (
                        <option key={vendor._id} value={vendor._id}>
                          {vendor.businessName}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                      Select Supplier
                    </label>
                    <select
                      value={selectedSupplier}
                      onChange={(e) => setSelectedSupplier(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '0.5rem',
                        border: '1px solid #ddd',
                        borderRadius: '4px'
                      }}
                      required
                    >
                      <option value="">Choose a supplier...</option>
                      {suppliers.map(supplier => (
                        <option key={supplier._id} value={supplier._id}>
                          {supplier.user?.name || 'Supplier'}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={sendMessage}>
              <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr auto', gap: '1rem' }}>
                  <input
                    type="text"
                    placeholder="Subject (optional)"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    style={{
                      padding: '0.5rem',
                      border: '1px solid #ddd',
                      borderRadius: '4px'
                    }}
                  />
                  <select
                    value={messageType}
                    onChange={(e) => setMessageType(e.target.value)}
                    style={{
                      padding: '0.5rem',
                      border: '1px solid #ddd',
                      borderRadius: '4px'
                    }}
                  >
                    <option value="text">Text</option>
                    <option value="request">Request</option>
                    <option value="offer">Offer</option>
                    <option value="notification">Notification</option>
                  </select>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <textarea
                  placeholder="Type your message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  rows="3"
                  style={{
                    flex: 1,
                    padding: '0.75rem',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    resize: 'vertical'
                  }}
                  required
                />
                <button
                  type="submit"
                  style={{
                    background: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    padding: '0.75rem 1.5rem',
                    cursor: 'pointer'
                  }}
                >
                  Send
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
