import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';

// Auth Pages
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';

// Main Pages
import Dashboard from './pages/Dashboard';
import UsersPage from './pages/UsersPage';
import VendorsPage from './pages/VendorsPage';
import SuppliersPage from './pages/SuppliersPage';
import CreateSupplierPage from './pages/CreateSupplierPage';
import ProductsPage from './pages/ProductsPage';
import CreateProductPage from './pages/CreateProductPage';
import OrdersPage from './pages/OrdersPage';
import CreateOrderPage from './pages/CreateOrderPage';
import FeedbackPage from './pages/FeedbackPage';
import CreateFeedbackPage from './pages/CreateFeedbackPage';
import MessagesPage from './pages/MessagesPage';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Layout><LoginPage /></Layout>} />
          <Route path="/register" element={<Layout><RegisterPage /></Layout>} />

          {/* Protected Routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Layout><Dashboard /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/users" element={
            <ProtectedRoute>
              <Layout><UsersPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/vendors" element={
            <ProtectedRoute>
              <Layout><VendorsPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/suppliers" element={
            <ProtectedRoute>
              <Layout><SuppliersPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/suppliers/create" element={
            <ProtectedRoute>
              <Layout><CreateSupplierPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/products" element={
            <ProtectedRoute>
              <Layout><ProductsPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/products/create" element={
            <ProtectedRoute>
              <Layout><CreateProductPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/orders" element={
            <ProtectedRoute>
              <Layout><OrdersPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/orders/create" element={
            <ProtectedRoute>
              <Layout><CreateOrderPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/feedback" element={
            <ProtectedRoute>
              <Layout><FeedbackPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/feedback/create" element={
            <ProtectedRoute>
              <Layout><CreateFeedbackPage /></Layout>
            </ProtectedRoute>
          } />

          <Route path="/messages" element={
            <ProtectedRoute>
              <Layout><MessagesPage /></Layout>
            </ProtectedRoute>
          } />

          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
