const { Server } = require("socket.io");
const Message = require("../models/Message");
const Supplier = require("../models/Supplier");
const Vendor = require("../models/Vendor");

let users = {};
let io;

const socketConnection = (server) => {
  io = new Server(server, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true,
    },
  });

  io.on("connection", (socket) => {
    console.log("User connected:", socket.id);

    socket.on("join", async ({ vendorId, supplierId }) => {
      try {
        const supplier = await Supplier.findById(supplierId);
        const vendor = await Vendor.findById(vendorId);
        if (!supplier || !vendor) return;

        const roomId = `${vendorId}_${supplierId}`;
        users[socket.id] = { vendorId, supplierId, roomId };
        socket.join(roomId);

        io.to(roomId).emit("user-joined", {
          vendorName: vendor.name,
          supplierName: supplier.name,
        });
      } catch (error) {
        console.error("Join error:", error.message);
        socket.disconnect();
      }
    });

    socket.on("chat", async ({ message }) => {
      const userData = users[socket.id];
      if (!userData) return;

      const { vendorId, supplierId, roomId } = userData;

      try {
        const savedMessage = await Message.create({
          vendor: vendorId,
          supplier: supplierId,
          content: message,
        });

        io.to(roomId).emit("receive", {
          vendorId,
          supplierId,
          message,
          timestamp: savedMessage.timestamp,
        });
      } catch (err) {
        console.error("Error saving message:", err.message);
      }
    });

    socket.on("disconnect", () => {
      const userData = users[socket.id];
      if (userData) {
        io.to(userData.roomId).emit("user-left", {
          vendorId: userData.vendorId,
          supplierId: userData.supplierId,
        });
        delete users[socket.id];
        console.log("User disconnected from room:", userData.roomId);
      }
    });
  });
};

const getIO = () => {
  if (!io) throw new Error("Socket.io not initialized");
  return io;
};

module.exports = { socketConnection, getIO };
