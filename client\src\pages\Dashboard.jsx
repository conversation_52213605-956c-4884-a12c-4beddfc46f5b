import api from "../api";
import { useState } from "react";

export default function Dashboard() {
  const [data, setData] = useState(null);

  const fetchData = async (endpoint) => {
    try {
      const res = await api.get(endpoint);
      setData(res.data);
    } catch (err) {
      alert(err.response?.data?.error || "Error fetching data");
    }
  };

  return (
    <div>
      <h2>Dashboard</h2>
      <button onClick={() => fetchData("/profile")}>Get All Users</button>
      <button onClick={() => fetchData("/vendors")}>Get All Vendors</button>
      <button onClick={() => fetchData("/suppliers")}>Get All Suppliers</button>
      <button onClick={() => fetchData("/products")}>Get All Products</button>
      <button onClick={() => fetchData("/orders")}>Get All Orders</button>
      <button onClick={() => fetchData("/feedback")}>Get All Feedback</button>

      <div>
        <h3>Result:</h3>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </div>
    </div>
  );
}
