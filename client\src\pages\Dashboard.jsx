import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import api from "../api";

export default function Dashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    users: 0,
    vendors: 0,
    suppliers: 0,
    products: 0,
    orders: 0,
    feedback: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const [usersRes, vendorsRes, suppliersRes, productsRes, ordersRes, feedbackRes] = await Promise.all([
        api.get("/users/profile").catch(() => ({ data: [] })),
        api.get("/vendors").catch(() => ({ data: [] })),
        api.get("/suppliers").catch(() => ({ data: [] })),
        api.get("/products").catch(() => ({ data: [] })),
        api.get("/orders").catch(() => ({ data: [] })),
        api.get("/feedbacks").catch(() => ({ data: [] }))
      ]);

      setStats({
        users: Array.isArray(usersRes.data) ? usersRes.data.length : 0,
        vendors: Array.isArray(vendorsRes.data) ? vendorsRes.data.length : 0,
        suppliers: Array.isArray(suppliersRes.data) ? suppliersRes.data.length : 0,
        products: Array.isArray(productsRes.data) ? productsRes.data.length : 0,
        orders: Array.isArray(ordersRes.data) ? ordersRes.data.length : 0,
        feedback: Array.isArray(feedbackRes.data) ? feedbackRes.data.length : 0
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, count, link, color = "#007bff" }) => (
    <Link to={link} style={{ textDecoration: 'none' }}>
      <div style={{
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '1.5rem',
        textAlign: 'center',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        transition: 'transform 0.2s',
        cursor: 'pointer'
      }}
      onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
      onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
      >
        <h3 style={{ color, margin: '0 0 0.5rem 0', fontSize: '2rem' }}>{count}</h3>
        <p style={{ color: '#666', margin: 0, fontSize: '1.1rem' }}>{title}</p>
      </div>
    </Link>
  );

  if (loading) {
    return <div style={{ textAlign: 'center', padding: '2rem' }}>Loading dashboard...</div>;
  }

  return (
    <div>
      <div style={{ marginBottom: '2rem' }}>
        <h1>Welcome to the Dashboard, {user?.name}!</h1>
        <p style={{ color: '#666', fontSize: '1.1rem' }}>
          Role: <strong>{user?.role}</strong> | Email: <strong>{user?.email}</strong>
        </p>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        <StatCard title="Total Users" count={stats.users} link="/users" color="#28a745" />
        <StatCard title="Total Vendors" count={stats.vendors} link="/vendors" color="#17a2b8" />
        <StatCard title="Total Suppliers" count={stats.suppliers} link="/suppliers" color="#ffc107" />
        <StatCard title="Total Products" count={stats.products} link="/products" color="#6f42c1" />
        <StatCard title="Total Orders" count={stats.orders} link="/orders" color="#fd7e14" />
        <StatCard title="Total Feedback" count={stats.feedback} link="/feedback" color="#e83e8c" />
      </div>

      <div style={{
        background: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '1.5rem'
      }}>
        <h3 style={{ marginTop: 0 }}>Quick Actions</h3>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          {user?.role === 'supplier' && (
            <Link to="/suppliers/create" style={{
              background: '#28a745',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              textDecoration: 'none'
            }}>
              Create Supplier Profile
            </Link>
          )}
          <Link to="/products/create" style={{
            background: '#007bff',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            textDecoration: 'none'
          }}>
            Add Product
          </Link>
          <Link to="/orders/create" style={{
            background: '#fd7e14',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            textDecoration: 'none'
          }}>
            Create Order
          </Link>
          <Link to="/feedback/create" style={{
            background: '#e83e8c',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            textDecoration: 'none'
          }}>
            Give Feedback
          </Link>
        </div>
      </div>
    </div>
  );
}
