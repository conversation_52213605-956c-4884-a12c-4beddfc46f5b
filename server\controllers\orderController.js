const Order = require("../models/Order");

exports.createOrder = async (req, res) => {
  try {
    const { sellerType, sellerId, products, totalAmount, deliveryAddress } = req.body;

    if (!sellerType || !sellerId || !products || !totalAmount || !deliveryAddress) {
      return res.status(400).json({
        success: false,
        message: "All fields (sellerType, sellerId, products, totalAmount, deliveryAddress) are required.",
      });
    }

    const validTypes = ["Vendor", "Supplier"];
    if (!validTypes.includes(sellerType)) {
      return res.status(400).json({
        success: false,
        message: "Invalid seller type. Must be Vendor or Supplier.",
      });
    }

    if (!Array.isArray(products) || products.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Products array must be provided and not empty.",
      });
    }
    for (const item of products) {
      if (!item.product || typeof item.quantity !== "number") {
        return res.status(400).json({
          success: false,
          message: "Each product must have a product ID and quantity.",
        });
      }
    }

    const order = await Order.create({
      user: req.user.id,
      sellerType,
      sellerId,
      products,
      totalAmount,
      deliveryAddress,
    });

    res.status(201).json({
      success: true,
      message: "Order placed successfully.",
      order,
    });
  } catch (err) {
    console.error("Create Order Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while creating order.",
    });
  }
};


exports.getAllOrders = async (req, res) => {
  try {
    const orders = await Order.find().populate("user").populate("products.product");
    res.json(orders);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id).populate("user").populate("products.product");
    if (!order) return res.status(404).json({ message: "Order not found" });
    res.json(order);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};