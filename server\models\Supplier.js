const mongoose = require("mongoose");

const supplierSchema = new mongoose.Schema(
  {
    user: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User", 
      required: true 
    },
    location: {
      type: {
        type: String,
        enum: ["Point"],
        default: "Point",
      },
      coordinates: {
        type: [Number],
      },
      address: {
        type: String,
      },
    },
    supplierRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 10,
    },
    products: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
      },
    ],
  },
  { timestamps: true }
);

supplierSchema.index({ location: "2dsphere" });

module.exports = mongoose.model("Supplier", supplierSchema);